"use client";

import React, { useState, useEffect, useRef } from 'react';

interface CrosshairProps {
  containerRef?: React.RefObject<HTMLElement>;
  color?: string;
  size?: number;
  thickness?: number;
  flickerOnHover?: boolean;
  className?: string;
}

const Crosshair: React.FC<CrosshairProps> = ({
  containerRef,
  color = '#00ff00',
  size = 20,
  thickness = 2,
  flickerOnHover = true,
  className = ''
}) => {
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isVisible, setIsVisible] = useState(true);
  const [isHovering, setIsHovering] = useState(false);
  const [flickerState, setFlickerState] = useState(true);
  const crosshairRef = useRef<HTMLDivElement>(null);

  // Flicker effect when hovering over interactive elements
  useEffect(() => {
    if (!isHovering || !flickerOnHover) {
      setFlickerState(true);
      return;
    }

    const flickerPattern = [100, 50, 100, 200, 50]; // Variable flicker pattern
    let patternIndex = 0;

    const scheduleNextFlicker = () => {
      setTimeout(() => {
        setFlickerState(prev => !prev);
        patternIndex = (patternIndex + 1) % flickerPattern.length;
        if (isHovering) scheduleNextFlicker();
      }, flickerPattern[patternIndex]);
    };

    scheduleNextFlicker();
  }, [isHovering, flickerOnHover]);

  // Mouse move handler
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      const container = containerRef?.current || document.body;
      const rect = container.getBoundingClientRect();
      
      setPosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });

      // Check if hovering over interactive elements
      const target = e.target as HTMLElement;
      const isInteractive = target.matches('button, a, [role="button"], input, select, textarea, .cursor-pointer') ||
                           target.closest('button, a, [role="button"], input, select, textarea, .cursor-pointer');
      
      setIsHovering(!!isInteractive);
    };

    const handleMouseEnter = () => setIsVisible(true);
    const handleMouseLeave = () => setIsVisible(false);

    const container = containerRef?.current || document;
    
    container.addEventListener('mousemove', handleMouseMove);
    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      container.removeEventListener('mousemove', handleMouseMove);
      container.removeEventListener('mouseenter', handleMouseEnter);
      container.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [containerRef]);

  if (!isVisible || !flickerState) return null;

  const crosshairStyle: React.CSSProperties = {
    position: 'absolute',
    left: position.x,
    top: position.y,
    width: size,
    height: size,
    pointerEvents: 'none',
    zIndex: 9999,
    transform: 'translate(-50%, -50%)',
    transition: isHovering ? 'none' : 'opacity 0.2s ease',
  };

  return (
    <div
      ref={crosshairRef}
      className={`crosshair ${className} ${isHovering ? 'crosshair-flicker' : ''}`}
      style={crosshairStyle}
    >
      {/* Outer tactical ring */}
      <div
        style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          width: size * 2,
          height: size * 2,
          border: `1px solid ${color}`,
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)',
          opacity: isHovering ? 0.6 : 0.3,
          boxShadow: `0 0 20px ${color}`,
        }}
      />

      {/* Tactical corner brackets */}
      {[0, 90, 180, 270].map((rotation, index) => (
        <div
          key={index}
          style={{
            position: 'absolute',
            left: '50%',
            top: '50%',
            width: size * 0.8,
            height: size * 0.8,
            transform: `translate(-50%, -50%) rotate(${rotation}deg)`,
            opacity: isHovering ? 0.9 : 0.6,
          }}
        >
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: size * 0.3,
              height: thickness,
              backgroundColor: color,
              boxShadow: `0 0 8px ${color}`,
            }}
          />
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: thickness,
              height: size * 0.3,
              backgroundColor: color,
              boxShadow: `0 0 8px ${color}`,
            }}
          />
        </div>
      ))}

      {/* Main crosshair lines */}
      <div
        style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          width: size,
          height: thickness,
          backgroundColor: color,
          transform: 'translate(-50%, -50%)',
          boxShadow: `0 0 12px ${color}`,
        }}
      />
      <div
        style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          width: thickness,
          height: size,
          backgroundColor: color,
          transform: 'translate(-50%, -50%)',
          boxShadow: `0 0 12px ${color}`,
        }}
      />

      {/* Center dot */}
      <div
        style={{
          position: 'absolute',
          left: '50%',
          top: '50%',
          width: thickness * 3,
          height: thickness * 3,
          backgroundColor: color,
          borderRadius: '50%',
          transform: 'translate(-50%, -50%)',
          boxShadow: `0 0 15px ${color}`,
          opacity: isHovering ? 1 : 0.8,
        }}
      />
    </div>
  );
};

export default Crosshair;
